<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Notification\Services\NotificationService;
use App\Modules\Setting\Services\GeneralSettingService;
use App\Modules\Setting\Constants\SettingKey;
use App\Util\Constant\UserDomainStatus;
use App\Util\Constant\DomainStatus;
use App\Util\Helper\DomainParser;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use Carbon\Carbon;

class DomainEppCancellationJobService
{
    public static function instance(): DomainEppCancellationJobService
    {
        return new self;
    }

    private function getRegisteredDomainId($domainId): int
    {
        $registeredDomain = DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->first();

        if (!$registeredDomain) {
            throw new \Exception("Registered domain not found for domain ID: {$domainId}");
        }

        return $registeredDomain->id;
    }

    public function eppDelete(array $domain): void
    {
        $eppInfoResponse = EppDomainService::instance()->callEppDomainInfo($domain['domainName']);
        $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName']);

        $eppInfo = $eppInfoResponse['data'];
        $datastoreInfo = $datastoreInfoResponse['data'];

        if (in_array('pendingTransfer', $eppInfo['status'])) {
            $this->handlePendingTransferRetry($domain);
            return;
        }

        if (in_array('clientDeleteProhibited', $eppInfo['status'])) {

            $updatePayload = [
                'name' => $domain['domainName'],
                'statusRemove' => ['clientDeleteProhibited']
            ];

            try {
                EppDomainService::instance()->updateEppDomain($updatePayload);
            } catch (\Exception $e) {
                NotificationService::sendFailedDeleteNotif($domain['domainName']);
                $this->revertDomainDeletion($domain);
                $this->dispatchDomainHistory($domain, 'failed');
                throw new \Exception("Failed to remove clientDeleteProhibited status for domain {$domain['domainName']}");
            }
        }

        $eppDeleteResult = EppDomainService::instance()->callEppDomainDelete($domain['domainName']);
        $datastoreDeleteResult = EppDomainService::instance()->callDatastoreDomainDelete($domain['domainName']);

        $eppSuccess = isset($eppDeleteResult['status']) && $eppDeleteResult['status'] === 'OK';
        $datastoreSuccess = isset($datastoreDeleteResult['status']) && $datastoreDeleteResult['status'] === 'OK';

        if ($eppSuccess && $datastoreSuccess) {
            $this->processRefundForExpiredDomain($domain, $datastoreInfo);
            $this->updateLocalDatabase($domain);
            $this->dispatchDomainHistory($domain, 'success');
        } else {
            NotificationService::sendFailedDeleteNotif($domain['domainName']);
            $this->revertDomainDeletion($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("EPP deletion failed for domain {$domain['domainName']}");
        }
    }

    private function handlePendingTransferRetry(array $domain): void
    {
        $retryAttempt = $domain['pendingTransferRetryAttempt'] ?? 0;
        $maxRetries = 3;

        if ($retryAttempt >= $maxRetries) {
            NotificationService::sendFailedDeletionNotifV2($domain['domainName']);
            $this->revertDomainDeletion($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("Domain {$domain['domainName']} has pendingTransfer status after {$maxRetries} retry attempts");
        }

        $nextAttempt = $retryAttempt + 1;
        $delayMinutes = 15;

        app(AuthLogger::class)->info("Domain {$domain['domainName']} has pendingTransfer status. Scheduling retry attempt {$nextAttempt}/{$maxRetries} in {$delayMinutes} minutes.");

        $domain['pendingTransferRetryAttempt'] = $nextAttempt;

        DomainEppCancellation::dispatch(
            $domain['domainId'],
            $domain['domainName'],
            $domain['userId'],
            $domain['userEmail'],
            $domain['reason'],
            $domain['createdDate'],
            $domain['supportNote'] ?? null,
            $domain['adminId'] ?? null,
            $domain['adminName'] ?? null,
            $domain['adminEmail'] ?? null,
            $nextAttempt
        )->delay(Carbon::now()->addMinutes($delayMinutes));
    }

    private function updateLocalDatabase(array $domain): void
    {
        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $domain['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update($updates);

        $this->updateDomainCancellationRequest($domain);

        $registeredDomainId = $this->getRegisteredDomainId($domain['domainId']);

        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $registeredDomainId,
            'deleted_by' => $domain['userEmail'],
            'deleted_at' => $timestamp,
            'created_at' => $timestamp,
            'updated_at' => $timestamp,
        ]);
    }

    private function updateDomainCancellationRequest(array $domain): void
    {
        $registeredDomainId = $this->getRegisteredDomainId($domain['domainId']);

        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->exists();

        if (!$exists) {
            $this->createDomainCancellationRequest($domain);
            return;
        }

        $adminId = $domain['adminId'] ?? 1;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";
        $supportNote = $domain['supportNote'] ?? "{$adminFullName} deleted domain: {$domain['domainName']}";

        DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->update([
                'support_agent_id'   => $adminId,
                'support_agent_name' => $adminFullName,
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
            ]);
    }

    private function createDomainCancellationRequest(array $domain): void
    {
        $adminId = $domain['adminId'] ?? 1;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";
        $supportNote = $domain['supportNote'] ?? "{$adminFullName} deleted domain: {$domain['domainName']}";
        $registeredDomainId = $this->getRegisteredDomainId($domain['domainId']);

        $existingRequest = DB::client()
            ->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->first();

        $isRefunded = $existingRequest ? $existingRequest->is_refunded : false;

        DB::client()->table('domain_cancellation_requests')->insert([
            'registered_domain_id' => $registeredDomainId,
            'reason'              => $domain['reason'],
            'support_agent_id'    => $adminId,
            'support_agent_name'  => $adminFullName,
            'deleted_at'          => now(),
            'feedback_date'       => now(),
            'support_note'        => $supportNote,
            'is_refunded'         => $isRefunded,
            'requested_at'        => now(),
        ]);
    }

    private function dispatchDomainHistory(array $domain, string $status): void
    {
        $adminName = $domain['adminName'] ?? 'System';
        
        if ($status === 'success') {
            $message = 'Domain "' . $domain['domainName'] . '" deleted by ' . $adminName ;
        } else {
            $message = 'Domain "' . $domain['domainName'] . '" deletion failed by ' . $adminName;
        }

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $domain['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $domain['userId'],
            'status'    => $status,
            'message'   => $message,
            'payload'   => json_encode($domain),
            'created_at'=> now(),
            'updated_at'=> now(),
        ]);
    }

    private function revertDomainDeletion(array $domain): void
    {
        $timestamp = now();

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update([
                'status' => DomainStatus::ACTIVE,
                'updated_at' => $timestamp,
            ]);

        DB::client()->table('registered_domains')
            ->where('domain_id', $domain['domainId'])
            ->update([
                'status' => UserDomainStatus::OWNED,
                'updated_at' => $timestamp,
            ]);

        $registeredDomainId = $this->getRegisteredDomainId($domain['domainId']);

        DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->update([
                'support_agent_id' => null,
                'support_agent_name' => null,
                'feedback_date' => null,
                'support_note' => 'Deletion request reverted due to EPP failure: ' . ($domain['domainName']),
                'deleted_at' => now(),
            ]);

        $this->sendReversionNotification($domain);
    }

    private function sendReversionNotification(array $domain): void
    {
        if (!isset($domain['userId']) || !isset($domain['domainName'])) {
            return;
        }

        $message = 'Your domain deletion request for "' . $domain['domainName'] . '" could not be processed due to technical issues. The domain has been reactivated and is now available for use. Please contact support if you still wish to delete this domain.';

        DB::client()->table('notifications')->insert([
            'user_id' => $domain['userId'],
            'title' => 'Domain Deletion Request Failed',
            'message' => $message,
            'redirect_url' => '/domain',
            'created_at' => now(),
            'updated_at' => now(),
            'importance' => 'important',
        ]);
    }

    private function processRefundForExpiredDomain(array $domain, array $datastoreInfo): void
    {
        try {
            $expiryDate = Carbon::parse($datastoreInfo['expiry']);
            $now = Carbon::now();

            if (!$expiryDate->isPast()) {
                return;
            }

            $daysExpired = $now->diffInDays($expiryDate);

            if ($daysExpired > 45) {
                return;
            }

            $registryId = DomainParser::getRegistryId($domain['domainName']);
            if (!$registryId) {
                return;
            }

            $registryBalance = RegistryAccountBalanceService::balance($registryId);

            // Get domain renewal cost and ICANN fee from settings
            $renewalCost = floatval(GeneralSettingService::getValueByKey(SettingKey::DOMAIN_RENEWAL));
            $icannFee = floatval(GeneralSettingService::getValueByKey(SettingKey::DOMAIN_ICANN_FEE));

            $refundAmount = $renewalCost - $icannFee;

            if ($refundAmount <= 0) {
                app(AuthLogger::class)->info("Refund amount for domain {$domain['domainName']} is {$refundAmount}. No refund processed.");
                return;
            }

            $description = "Refund for expired domain deletion: {$domain['domainName']} (expired {$daysExpired} days ago)";

            $result = RegistryAccountBalanceService::debit(
                $registryBalance,
                $refundAmount,
                RegistryTransactionType::REFUND_TRANSACTION,
                $description
            );

            if ($result === true) {
                app(AuthLogger::class)->info("Successfully processed refund of \${$refundAmount} for domain {$domain['domainName']} (Registry ID: {$registryId})");
            } else {
                app(AuthLogger::class)->error("Failed to process refund for domain {$domain['domainName']}. Registry balance service returned error.");
            }

        } catch (\Exception $e) {
            app(AuthLogger::class)->error("Error processing refund for domain {$domain['domainName']}: " . $e->getMessage());
        }
    }
}
